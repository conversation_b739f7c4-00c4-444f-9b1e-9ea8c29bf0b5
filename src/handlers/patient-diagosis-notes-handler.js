const logging = require('../common/logging')
const patientService = require('../services/patient-service')
const { DiagnosisStatus, ActivityStatus } = require('../common/constant')

class PatientDiagnosisNotesHandler {
  async getPatientDiagnosisNotes(patientId) {
    try {
      return await patientService.getPatientDiagnosisNotes(patientId)
    } catch (error) {
      logging.logError(
        `Unable to get patient diagnosis notes for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async createPatientDiagnosisNotes(patientId, diagnosisNotes, create_by) {
    try {
      diagnosisNotes.diagnosisStatus = DiagnosisStatus.PROVISIONAL
      diagnosisNotes.activityStatus = ActivityStatus.ACTIVE
      diagnosisNotes.create_by = create_by
      diagnosisNotes.update_by = create_by
      return await patientService.createPatientDiagnosisNotes(
        patientId,
        diagnosisNotes,
      )
    } catch (error) {
      logging.logError(
        `Unable to create patient diagnosis notes for patient ${patientId}`,
        error,
      )
      return null
    }
  }

  async patchPatientDiagnosisNotes(id, diagnosisNotes, update_by) {
    try {
      const existingNote = await patientService.getPatientDiagnosisNoteById(id)
      if (!existingNote) {
        throw new Error('Diagnosis note not found')
      }

      if (diagnosisNotes.records && Array.isArray(diagnosisNotes.records)) {
        const existingRecordsMap = new Map()
        if (existingNote.records && Array.isArray(existingNote.records)) {
          existingNote.records.forEach(record => {
            existingRecordsMap.set(record.record_id, record)
          })
        }

        for (const record of diagnosisNotes.records) {
          if (record.record_id) {
            const existingRecord = existingRecordsMap.get(record.record_id)
            
            if (existingRecord) {
              if (record.diagnosisStatus) {
                // Once confirmed, cannot change back to provisional
                if (
                  existingRecord.diagnosisStatus === DiagnosisStatus.CONFIRMED &&
                  record.diagnosisStatus === DiagnosisStatus.PROVISIONAL
                ) {
                  throw new Error(
                    `Cannot change diagnosis status from confirmed back to provisional for record ${record.record_id}`
                  )
                }

                if (!Object.values(DiagnosisStatus).includes(record.diagnosisStatus)) {
                  throw new Error(
                    `Invalid diagnosis status for record ${record.record_id}. Must be one of: ${Object.values(DiagnosisStatus).join(', ')}`
                  )
                }
              }

              if (record.activityStatus) {
                if (!Object.values(ActivityStatus).includes(record.activityStatus)) {
                  throw new Error(
                    `Invalid activity status for record ${record.record_id}. Must be one of: ${Object.values(ActivityStatus).join(', ')}`
                  )
                }
              }
            }
          }

          if (record.diagnosisStatus && !Object.values(DiagnosisStatus).includes(record.diagnosisStatus)) {
            throw new Error(
              `Invalid diagnosis status. Must be one of: ${Object.values(DiagnosisStatus).join(', ')}`
            )
          }

          if (record.activityStatus && !Object.values(ActivityStatus).includes(record.activityStatus)) {
            throw new Error(
              `Invalid activity status. Must be one of: ${Object.values(ActivityStatus).join(', ')}`
            )
          }
        }
      }

      if (diagnosisNotes.diagnosisStatus) {
        if (
          existingNote.diagnosisStatus === DiagnosisStatus.CONFIRMED &&
          diagnosisNotes.diagnosisStatus === DiagnosisStatus.PROVISIONAL
        ) {
          throw new Error(
            'Cannot change diagnosis status from confirmed back to provisional',
          )
        }

        if (
          !Object.values(DiagnosisStatus).includes(
            diagnosisNotes.diagnosisStatus,
          )
        ) {
          throw new Error(
            `Invalid diagnosis status. Must be one of: ${Object.values(
              DiagnosisStatus,
            ).join(', ')}`,
          )
        }
      }

      if (diagnosisNotes.activityStatus) {
        if (
          !Object.values(ActivityStatus).includes(diagnosisNotes.activityStatus)
        ) {
          throw new Error(
            `Invalid activity status. Must be one of: ${Object.values(
              ActivityStatus,
            ).join(', ')}`,
          )
        }
      }

      diagnosisNotes.update_by = update_by
      return await patientService.patchPatientDiagnosisNotes(id, diagnosisNotes)
    } catch (error) {
      logging.logError(
        `Unable to patch patient diagnosis notes with id ${id}`,
        error,
      )
      throw error
    }
  }

  async getPatientDiagnosisNotesByQuery(query) {
    try {
      var data = await patientService.getPatientDiagnosisNotesByQuery(query)
      
      if (data && data.records && Array.isArray(data.records)) {
        data.records = data.records.map(record => ({
          ...record,
          ...(record.icdCode && { icdCode: record.icdCode })
        }))
      }
      
      return data
    } catch (error) {
      logging.logError(
        `Unable to get patient diagnosis notes by query ${query}`,
        error,
      )
      return null
    }
  }
}

module.exports = new PatientDiagnosisNotesHandler()
