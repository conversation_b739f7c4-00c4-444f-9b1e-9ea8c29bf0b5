const { app } = require('@azure/functions')
const { HttpMethod } = require('../common/constant')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const patientDiagosisNotesHandler = require('../handlers/patient-diagosis-notes-handler')

app.http('patient-diagnosis-notes', {
  methods: ['GET', 'POST', 'PATCH'],
  authLevel: 'function',
  route: 'patient/diagnosis-notes',
  handler: async (request, context) => {
    context.log(`Http function processed request for url "${request.url}"`)
    const decode = context.extraInputs.get('decode')
    const patientId = request.query.get('patientId')

    switch (request.method) {
      case HttpMethod.get:
        const query = request.query.get('query')
        const diagnosisStatus = request.query.get('diagnosisStatus')
        const activityStatus = request.query.get('activityStatus')

        if (!patientId && !query) {
          return jsonResponse(
            `Missing PatientId or query`,
            HttpStatusCode.BadRequest,
          )
        }

        if (query) {
          var data =
            await patientDiagosisNotesHandler.getPatientDiagnosisNotesByQuery(
              query,
            )

          // Apply status filters if provided
          if (data && data.records && Array.isArray(data.records)) {
            let filteredRecords = data.records
            console.log('Original records count:', filteredRecords.length)
            console.log(
              'Filter criteria - diagnosisStatus:',
              diagnosisStatus,
              'activityStatus:',
              activityStatus,
            )

            // Filter by diagnosisStatus if provided
            if (diagnosisStatus) {
              const beforeCount = filteredRecords.length
              filteredRecords = filteredRecords.filter(
                (record) => record.diagnosisStatus === diagnosisStatus,
              )
              console.log(
                `After diagnosisStatus filter: ${filteredRecords.length}/${beforeCount} records`,
              )
            }

            // Filter by activityStatus if provided (check both activityStatus and status fields)
            if (activityStatus) {
              const beforeCount = filteredRecords.length
              filteredRecords = filteredRecords.filter(
                (record) =>
                  record.activityStatus === activityStatus ||
                  record.status === activityStatus,
              )
              console.log(
                `After activityStatus filter: ${filteredRecords.length}/${beforeCount} records`,
              )
            }

            console.log(
              'Final filtered records:',
              filteredRecords.map((r) => ({
                record_id: r.record_id,
                diagnosisStatus: r.diagnosisStatus,
                activityStatus: r.activityStatus,
                status: r.status,
              })),
            )

            data.records = filteredRecords
          }

          return jsonResponse(data)
        }
        var data = await patientDiagosisNotesHandler.getPatientDiagnosisNotes(
          patientId,
        )
        console.log(data, 'jjjjjjjjjjjj')

        // Apply status filters to patient-specific data if provided
        if (data && data[0].records && Array.isArray(data[0].records)) {
          let filteredRecords = data[0].records
          console.log('Patient-specific records count:', filteredRecords.length)

          // Filter by diagnosisStatus if provided
          if (diagnosisStatus) {
            const beforeCount = filteredRecords.length
            filteredRecords = filteredRecords.filter(
              (record) => record.diagnosisStatus === diagnosisStatus,
            )
            console.log(
              `Patient-specific diagnosisStatus filter: ${filteredRecords.length}/${beforeCount} records`,
            )
          }

          // Filter by activityStatus if provided (check both activityStatus and status fields)
          if (activityStatus) {
            const beforeCount = filteredRecords.length
            filteredRecords = filteredRecords.filter(
              (record) =>
                record.activityStatus === activityStatus ||
                record.status === activityStatus,
            )
            console.log(
              `Patient-specific activityStatus filter: ${filteredRecords.length}/${beforeCount} records`,
            )
          }

          console.log(
            'Final patient-specific filtered records:',
            filteredRecords.map((r) => ({
              record_id: r.record_id,
              diagnosisStatus: r.diagnosisStatus,
              activityStatus: r.activityStatus,
              status: r.status,
            })),
          )

          data.records = filteredRecords
          console.log(data, 'kkkkkkkkkkkkkkkkk')
        }

        return jsonResponse(data)
      case HttpMethod.post:
        if (!request.body || !patientId) {
          return jsonResponse(
            `Missing request payload or patientId`,
            HttpStatusCode.BadRequest,
          )
        }
        const diagnosisNotes = await request.json()
        var newdata = null
        if (HttpMethod.post) {
          newdata =
            await patientDiagosisNotesHandler.createPatientDiagnosisNotes(
              patientId,
              diagnosisNotes,
              decode.oid,
            )
        }
        return jsonResponse(newdata)
      case HttpMethod.patch:
        const id = request.query.get('id')
        const patchData = await request.json()
        if (!id || !patchData) {
          return jsonResponse(
            `Missing id or patch data`,
            HttpStatusCode.BadRequest,
          )
        }
        try {
          var updatedData =
            await patientDiagosisNotesHandler.patchPatientDiagnosisNotes(
              id,
              patchData,
              decode.oid,
            )
          return jsonResponse(updatedData)
        } catch (error) {
          return jsonResponse(error.message, HttpStatusCode.BadRequest)
        }
      default:
        return jsonResponse(
          `Unsupported HTTP method`,
          HttpStatusCode.MethodNotAllowed,
        )
    }
  },
})
