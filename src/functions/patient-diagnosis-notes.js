const { app } = require('@azure/functions')
const { HttpMethod } = require('../common/constant')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const patientDiagosisNotesHandler = require('../handlers/patient-diagosis-notes-handler')

app.http('patient-diagnosis-notes', {
  methods: ['GET', 'POST', 'PATCH'],
  authLevel: 'function',
  route: 'patient/diagnosis-notes',
  handler: async (request, context) => {
    context.log(`Http function processed request for url "${request.url}"`)
    const decode = context.extraInputs.get('decode')
    const patientId = request.query.get('patientId')

    switch (request.method) {
      case HttpMethod.get:
        const query = request.query.get('query')
        const diagnosisStatus = request.query.get('diagnosisStatus')
        const activityStatus = request.query.get('activityStatus')

        if (!patientId && !query) {
          return jsonResponse(
            `Missing PatientId or query`,
            HttpStatusCode.BadRequest,
          )
        }

        if (query) {
          var data =
            await patientDiagosisNotesHandler.getPatientDiagnosisNotesByQuery(
              query,
            )

          if (data && data.records && Array.isArray(data.records)) {
            let filteredRecords = data.records

            if (diagnosisStatus) {
              filteredRecords = filteredRecords.filter(
                (record) => record.diagnosisStatus === diagnosisStatus,
              )
            }

            if (activityStatus) {
              filteredRecords = filteredRecords.filter(
                (record) => record.activityStatus === activityStatus,
              )
            }

            data.records = filteredRecords
          }

          return jsonResponse(data)
        }
        var data = await patientDiagosisNotesHandler.getPatientDiagnosisNotes(
          patientId,
        )

        if (data && data[0].records && Array.isArray(data[0].records)) {
          let filteredRecords = data[0].records

          if (diagnosisStatus) {
            filteredRecords = filteredRecords.filter(
              (record) => record.diagnosisStatus === diagnosisStatus,
            )
          }

          if (activityStatus) {
            filteredRecords = filteredRecords.filter(
              (record) => record.activityStatus === activityStatus,
            )
          }

          data[0].records = filteredRecords
        }

        return jsonResponse(data)
      case HttpMethod.post:
        if (!request.body || !patientId) {
          return jsonResponse(
            `Missing request payload or patientId`,
            HttpStatusCode.BadRequest,
          )
        }
        const diagnosisNotes = await request.json()
        var newdata = null
        if (HttpMethod.post) {
          newdata =
            await patientDiagosisNotesHandler.createPatientDiagnosisNotes(
              patientId,
              diagnosisNotes,
              decode.oid,
            )
        }
        return jsonResponse(newdata)
      case HttpMethod.patch:
        const id = request.query.get('id')
        const patchData = await request.json()
        if (!id || !patchData) {
          return jsonResponse(
            `Missing id or patch data`,
            HttpStatusCode.BadRequest,
          )
        }
        try {
          var updatedData =
            await patientDiagosisNotesHandler.patchPatientDiagnosisNotes(
              id,
              patchData,
              decode.oid,
            )
          return jsonResponse(updatedData)
        } catch (error) {
          return jsonResponse(error.message, HttpStatusCode.BadRequest)
        }
      default:
        return jsonResponse(
          `Unsupported HTTP method`,
          HttpStatusCode.MethodNotAllowed,
        )
    }
  },
})
