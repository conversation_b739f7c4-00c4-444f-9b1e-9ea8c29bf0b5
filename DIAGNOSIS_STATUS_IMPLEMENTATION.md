# Patient Diagnosis Notes - Status Fields Implementation

## Overview
Successfully implemented two status fields for patient diagnosis notes API as requested:

1. **Diagnosis Status**: `provisional` or `confirmed`
2. **Activity Status**: `active` or `inactive`

## Business Rules Implemented

### Status Transitions
- **Initial Creation**: All new diagnosis notes default to `provisional` + `active`
- **Doctor Updates**: Can update to `confirmed` + `active` or `confirmed` + `inactive`
- **Restriction**: Once `confirmed`, cannot change back to `provisional` (enforced with validation)

### Validation Rules
- Only valid diagnosis status values: `provisional`, `confirmed`
- Only valid activity status values: `active`, `inactive`
- Prevents downgrade from `confirmed` to `provisional`
- Returns appropriate error messages for invalid operations

## Files Modified

### 1. Constants (`src/common/constant.js`)
```javascript
const DiagnosisStatus = Object.freeze({
  PROVISIONAL: 'provisional',
  CONFIRMED: 'confirmed',
})

const ActivityStatus = Object.freeze({
  ACTIVE: 'active',
  INACTIVE: 'inactive',
})
```

### 2. Hand<PERSON> (`src/handlers/patient-diagosis-notes-handler.js`)
- **POST**: Automatically sets default status values (`provisional` + `active`)
- **PATCH**: Validates status transitions and values before updating
- Added comprehensive validation logic with detailed error messages

### 3. Service (`src/services/patient-service.js`)
- Added `getPatientDiagnosisNoteById()` method for retrieving individual diagnosis notes
- Required for validation during PATCH operations

### 4. Function (`src/functions/patient-diagnosis-notes.js`)
- Enhanced PATCH endpoint with proper error handling
- Returns validation errors with HTTP 400 status code

## API Usage Examples

### Create Diagnosis Note (POST)
```bash
POST /api/patient/diagnosis-notes?patientId=test-patient-123
Content-Type: application/json

{
  "content": "<div>5C51.3 - Glycogen storage disease</div>",
  "doctor_id": "c97bd918-d3f1-4d4b-8851-8c8b09f0a405",
  "field": "Diagnosis",
  "record_id": "R1754890142984",
  "timestamp": "2025-08-11T05:29:02.984Z",
  "version": 1
}
```

**Response:**
```json
{
  "id": "dfb6bf50-fdfe-443d-8d6f-96bd9a57ac89",
  "diagnosisStatus": "provisional",
  "activityStatus": "active",
  "content": "<div>5C51.3 - Glycogen storage disease</div>",
  "doctor_id": "c97bd918-d3f1-4d4b-8851-8c8b09f0a405",
  "field": "Diagnosis",
  "record_id": "R1754890142984",
  "timestamp": "2025-08-11T05:29:02.984Z",
  "version": 1,
  "created_on": "2025-08-11T05:54:49.588Z",
  "updated_on": "2025-08-11T05:54:49.588Z"
}
```

### Update to Confirmed + Active (PATCH)
```bash
PATCH /api/patient/diagnosis-notes?id=dfb6bf50-fdfe-443d-8d6f-96bd9a57ac89
Content-Type: application/json

{
  "diagnosisStatus": "confirmed",
  "activityStatus": "active"
}
```

### Update to Confirmed + Inactive (PATCH)
```bash
PATCH /api/patient/diagnosis-notes?id=dfb6bf50-fdfe-443d-8d6f-96bd9a57ac89
Content-Type: application/json

{
  "activityStatus": "inactive"
}
```

### Invalid Operations (Return HTTP 400)

#### Attempt to downgrade from confirmed to provisional:
```bash
PATCH /api/patient/diagnosis-notes?id=dfb6bf50-fdfe-443d-8d6f-96bd9a57ac89
Content-Type: application/json

{
  "diagnosisStatus": "provisional"
}
```
**Error Response:** `"Cannot change diagnosis status from confirmed back to provisional"`

#### Invalid status value:
```bash
PATCH /api/patient/diagnosis-notes?id=dfb6bf50-fdfe-443d-8d6f-96bd9a57ac89
Content-Type: application/json

{
  "diagnosisStatus": "invalid_status"
}
```
**Error Response:** `"Invalid diagnosis status. Must be one of: provisional, confirmed"`

## Testing Results

All tests passed successfully:

✅ **Test 1**: Create diagnosis note - defaults to `provisional` + `active`  
✅ **Test 2**: Update to `confirmed` + `active`  
✅ **Test 3**: Update to `confirmed` + `inactive`  
✅ **Test 4**: Correctly reject downgrade to `provisional`  
✅ **Test 5**: Correctly reject invalid status values  
✅ **Test 6**: Retrieve all diagnosis notes with status fields  

## Database Schema

The implementation uses the existing `PatientDiagnosisNotes` Cosmos DB container with two new fields:

- `diagnosisStatus`: String (`"provisional"` | `"confirmed"`)
- `activityStatus`: String (`"active"` | `"inactive"`)

## Backward Compatibility

- Existing diagnosis notes without status fields will continue to work
- New diagnosis notes automatically get default status values
- No breaking changes to existing API contracts

## Production Deployment Notes

1. Update environment variable from `"local_"` back to production value in `local.settings.json`
2. Ensure proper authentication is enabled for production environment
3. Test with actual JWT tokens in production environment
4. Monitor logs for any validation errors during initial rollout

## Summary

The implementation successfully adds the requested two status fields to patient diagnosis notes with proper validation, business rule enforcement, and comprehensive testing. The solution maintains backward compatibility while providing the required functionality for managing diagnosis status transitions.
